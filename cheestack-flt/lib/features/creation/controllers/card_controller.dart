import 'package:get/get.dart';
import 'package:flutter/material.dart';

import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/shared/utils/error_handler.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/widgets/index.dart';

/// 专用的卡片编辑控制器
/// 负责处理卡片的创建、编辑、验证等逻辑
/// 现在完全使用本地数据库保存，不与后端API通讯
class CardController extends GetxController {
  // 本地数据服务
  CardDataService? _cardDataService;

  // 响应式状态
  final _isLoading = false.obs;
  final _isSaving = false.obs;
  final _card = Rx<CardModel?>(null);
  final _isFormValid = false.obs;

  // 响应式文本内容（用于实时预览）
  final _titleText = ''.obs;
  final _questionText = ''.obs;
  final _answerText = ''.obs;

  // 表单控制器
  late TextEditingController titleController;
  late TextEditingController questionController;
  late TextEditingController answerController;

  // 表单验证
  final formKey = GlobalKey<FormState>();

  // 书籍ID（卡片必须属于某个书籍）
  int? bookId;

  // Getters
  bool get isLoading => _isLoading.value;
  bool get isSaving => _isSaving.value;
  CardModel? get card => _card.value;
  bool get isFormValid => _isFormValid.value;
  bool get isNewCard => card?.isNew ?? true;

  // 响应式文本内容getters
  String get titleText => _titleText.value;
  String get questionText => _questionText.value;
  String get answerText => _answerText.value;

  CardController() {
    _initializeServices();
    _initializeControllers();
  }

  @override
  void onInit() {
    super.onInit();
    _initFromArguments();
  }

  /// 初始化服务
  void _initializeServices() {
    if (Get.isRegistered<CardDataService>()) {
      _cardDataService = CardDataService.to;
    }
  }

  /// 从路由参数初始化
  void _initFromArguments() {
    final arguments = Get.arguments;
    if (arguments is Map<String, dynamic>) {
      if (arguments['card'] is CardModel) {
        setCard(arguments['card'] as CardModel);
      }
      if (arguments['bookId'] is int) {
        bookId = arguments['bookId'] as int;
      }
    } else if (arguments is CardModel) {
      setCard(arguments);
    }

    Console.log('CardController初始化完成，bookId: $bookId');
  }

  @override
  void onClose() {
    _disposeControllers();
    super.onClose();
  }

  /// 初始化控制器
  void _initializeControllers() {
    titleController = TextEditingController();
    questionController = TextEditingController();
    answerController = TextEditingController();

    // 监听文本变化以验证表单和更新响应式变量
    titleController.addListener(() {
      _titleText.value = titleController.text;
      _validateForm();
    });
    questionController.addListener(() {
      _questionText.value = questionController.text;
      _validateForm();
    });
    answerController.addListener(() {
      _answerText.value = answerController.text;
      _validateForm();
    });
  }

  /// 释放控制器
  void _disposeControllers() {
    titleController.dispose();
    questionController.dispose();
    answerController.dispose();
  }

  /// 设置要编辑的卡片
  void setCard(CardModel cardModel) {
    _card.value = cardModel;
    _populateControllers();
    _validateForm();
  }

  /// 填充控制器内容
  void _populateControllers() {
    if (card != null) {
      titleController.text = card!.title ?? '';
      questionController.text = card!.question ?? '';
      answerController.text = card!.answer ?? '';

      // 同时更新响应式变量
      _titleText.value = card!.title ?? '';
      _questionText.value = card!.question ?? '';
      _answerText.value = card!.answer ?? '';
    }
  }

  /// 验证表单
  void _validateForm() {
    final isValid = titleController.text.trim().isNotEmpty;
    _isFormValid.value = isValid;
  }

  /// 保存卡片
  Future<bool> saveCard() async {
    if (!_validateAndShowErrors()) {
      return false;
    }

    _isSaving.value = true;

    try {
      if (isNewCard) {
        await _createCard();
      } else {
        await _updateCard();
      }

      try {
        ShowToast.success(isNewCard ? '卡片创建成功' : '卡片更新成功');
      } catch (e) {
        // 在测试环境中，ShowToast可能不可用，忽略错误
      }
      return true;
    } catch (e) {
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      try {
        ShowToast.fail(friendlyMessage);
      } catch (toastError) {
        // 在测试环境中，ShowToast可能不可用，忽略错误
      }
      return false;
    } finally {
      _isSaving.value = false;
    }
  }

  /// 创建新卡片 - 强制本地优先
  Future<void> _createCard() async {
    Console.log('开始创建卡片...');
    Console.log('CardDataService 是否可用: ${_cardDataService != null}');
    Console.log('书籍ID: $bookId');

    CardModel? result;

    if (_cardDataService != null) {
      Console.log('使用本地服务创建卡片（强制本地优先）');
      Console.log('卡片标题: ${titleController.text.trim()}');
      Console.log('卡片问题: ${questionController.text.trim()}');
      Console.log('卡片答案: ${answerController.text.trim()}');

      // 验证bookId
      int effectiveBookId;
      if (bookId != null) {
        effectiveBookId = bookId!;
      } else {
        // 如果没有bookId，尝试获取用户的第一个书籍或创建默认书籍
        try {
          final bookDataService = Get.find<BookDataService>();
          final userBooks = await bookDataService.getUserBooks(limit: 1);
          if (userBooks.isNotEmpty) {
            effectiveBookId = userBooks.first.id!;
            Console.log('使用用户的第一个书籍ID: $effectiveBookId');
          } else {
            // 创建默认书籍
            final defaultBook = await bookDataService.createBook(
              name: '默认书籍',
              brief: '系统自动创建的默认书籍',
            );
            if (defaultBook != null) {
              effectiveBookId = defaultBook.id!;
              Console.log('创建了默认书籍，ID: $effectiveBookId');
            } else {
              throw Exception('无法创建默认书籍');
            }
          }
        } catch (e) {
          Console.log('获取或创建书籍失败: $e');
          throw Exception('请先选择一个书籍或创建新书籍');
        }
      }

      Console.log('使用的bookId: $effectiveBookId');

      try {
        result = await _cardDataService!.createCard(
          bookId: effectiveBookId,
          title: titleController.text.trim(),
          question: questionController.text.trim(),
          answer: answerController.text.trim(),
          type: 'general',
          typeVersion: 1,
          extra: {},
        );

        Console.log('本地创建结果: $result');

        if (result != null) {
          try {
            ShowToast.success('卡片已保存到本地数据库');
          } catch (e) {
            // 在测试环境中，ShowToast可能不可用，忽略错误
          }
        } else {
          throw Exception('保存失败，请稍后重试');
        }
      } catch (e) {
        Console.log('本地保存失败，错误: $e');
        // 提供更友好的错误消息
        String friendlyMessage = '保存失败';
        if (e.toString().contains('FOREIGN KEY constraint failed')) {
          friendlyMessage = '书籍不存在，请重新选择书籍';
        } else if (e.toString().contains('NOT NULL constraint failed')) {
          friendlyMessage = '必填字段不能为空';
        } else if (e.toString().contains('UNIQUE constraint failed')) {
          friendlyMessage = '卡片已存在，请检查标题';
        }
        throw Exception(friendlyMessage);
      }
    } else {
      Console.log('CardDataService不可用，无法进行本地保存');
      throw Exception('数据服务不可用，请重启应用');
    }

    _card.value = result;
  }

  /// 更新现有卡片 - 强制本地优先
  Future<void> _updateCard() async {
    if (card?.id == null) {
      throw Exception('卡片ID不能为空');
    }

    Console.log('开始更新卡片...');
    Console.log('CardDataService 是否可用: ${_cardDataService != null}');
    Console.log('卡片ID: ${card!.id}');

    CardModel? result;

    if (_cardDataService != null) {
      Console.log('使用本地服务更新卡片（强制本地优先）');

      try {
        final success = await _cardDataService!.updateCard(
          card!.id!,
          title: titleController.text.trim(),
          question: questionController.text.trim(),
          answer: answerController.text.trim(),
          extra: card!.extra as Map<String, dynamic>? ?? {},
        );

        if (success) {
          result = card!.copyWith(
            title: titleController.text.trim(),
            question: questionController.text.trim(),
            answer: answerController.text.trim(),
            updatedAt: DateTime.now(),
          );
          try {
            ShowToast.success('卡片已更新到本地数据库');
          } catch (e) {
            // 在测试环境中，ShowToast可能不可用，忽略错误
          }
        } else {
          throw Exception('本地更新失败');
        }
      } catch (e) {
        Console.log('本地更新失败，错误: $e');
        // 提供更友好的错误消息
        String friendlyMessage = '更新失败';
        if (e.toString().contains('NOT NULL constraint failed')) {
          friendlyMessage = '必填字段不能为空';
        } else if (e.toString().contains('UNIQUE constraint failed')) {
          friendlyMessage = '卡片标题已存在';
        } else if (e.toString().contains('no such table')) {
          friendlyMessage = '数据库错误，请重启应用';
        }
        throw Exception(friendlyMessage);
      }
    } else {
      Console.log('CardDataService不可用，无法进行本地更新');
      throw Exception('数据服务不可用，请重启应用');
    }

    _card.value = result;
  }

  /// 验证表单并显示错误
  bool _validateAndShowErrors() {
    // 基本验证：检查标题是否为空
    if (titleController.text.trim().isEmpty) {
      try {
        ShowToast.fail('标题不能为空');
      } catch (e) {
        // 在测试环境中，ShowToast可能不可用，忽略错误
      }
      return false;
    }

    // 如果表单键可用，则进行完整验证
    try {
      if (formKey.currentState?.validate() != true) {
        return false;
      }
    } catch (e) {
      // 在测试环境中，formKey可能不可用，跳过表单验证
      // 只进行基本的字段验证
    }

    return true;
  }

  /// 重置表单
  void resetForm() {
    titleController.clear();
    questionController.clear();
    answerController.clear();
    _card.value = null;

    // 重置响应式变量
    _titleText.value = '';
    _questionText.value = '';
    _answerText.value = '';

    _validateForm();
  }

  /// 检查是否有未保存的更改
  bool hasUnsavedChanges() {
    if (card == null) {
      return titleController.text.trim().isNotEmpty ||
          questionController.text.trim().isNotEmpty ||
          answerController.text.trim().isNotEmpty;
    }

    return titleController.text != (card!.title ?? '') ||
        questionController.text != (card!.question ?? '') ||
        answerController.text != (card!.answer ?? '');
  }

  /// 显示放弃更改确认对话框
  Future<bool> showDiscardChangesDialog() async {
    if (!hasUnsavedChanges()) {
      return true;
    }

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const OxText('放弃更改'),
        content: const OxText('您有未保存的更改，确定要放弃吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const OxText('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const OxText('放弃'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// 字段验证器
  String? validateTitle(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '标题不能为空';
    }
    if (value.trim().length > 100) {
      return '标题不能超过100个字符';
    }
    return null;
  }

  String? validateQuestion(String? value) {
    if (value != null && value.trim().length > 1000) {
      return '问题不能超过1000个字符';
    }
    return null;
  }

  String? validateAnswer(String? value) {
    if (value != null && value.trim().length > 1000) {
      return '答案不能超过1000个字符';
    }
    return null;
  }
}
