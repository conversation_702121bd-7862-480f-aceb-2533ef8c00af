// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in cheestack_flt/test/fixes_verification_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:cheestack_flt/models/index.dart' as _i6;
import 'package:cheestack_flt/services/index.dart' as _i3;
import 'package:get/get.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;
import 'package:sqflite/sqflite.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeInternalFinalCallback_0<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCardDataService_1 extends _i1.SmartFake
    implements _i3.CardDataService {
  _FakeCardDataService_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBookDataService_2 extends _i1.SmartFake
    implements _i3.BookDataService {
  _FakeBookDataService_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserDataService_3 extends _i1.SmartFake
    implements _i3.UserDataService {
  _FakeUserDataService_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabase_4 extends _i1.SmartFake implements _i4.Database {
  _FakeDatabase_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabaseService_5 extends _i1.SmartFake
    implements _i3.DatabaseService {
  _FakeDatabaseService_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFuture_6<T1> extends _i1.SmartFake implements _i5.Future<T1> {
  _FakeFuture_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [CardDataService].
///
/// See the documentation for Mockito's code generation for more information.
class MockCardDataService extends _i1.Mock implements _i3.CardDataService {
  @override
  _i2.InternalFinalCallback<void> get onStart => (super.noSuchMethod(
        Invocation.getter(#onStart),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete => (super.noSuchMethod(
        Invocation.getter(#onDelete),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized => (super.noSuchMethod(
        Invocation.getter(#initialized),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i5.Future<_i3.CardDataService> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue:
            _i5.Future<_i3.CardDataService>.value(_FakeCardDataService_1(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i5.Future<_i3.CardDataService>.value(_FakeCardDataService_1(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
      ) as _i5.Future<_i3.CardDataService>);

  @override
  _i5.Future<List<_i6.CardModel>> getBookCards(
    int? bookId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBookCards,
          [bookId],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i5.Future<List<_i6.CardModel>>.value(<_i6.CardModel>[]),
        returnValueForMissingStub:
            _i5.Future<List<_i6.CardModel>>.value(<_i6.CardModel>[]),
      ) as _i5.Future<List<_i6.CardModel>>);

  @override
  _i5.Future<List<_i6.CardModel>> getUserCards(
    int? userId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserCards,
          [userId],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i5.Future<List<_i6.CardModel>>.value(<_i6.CardModel>[]),
        returnValueForMissingStub:
            _i5.Future<List<_i6.CardModel>>.value(<_i6.CardModel>[]),
      ) as _i5.Future<List<_i6.CardModel>>);

  @override
  _i5.Future<_i6.CardModel?> getCardById(int? cardId) => (super.noSuchMethod(
        Invocation.method(
          #getCardById,
          [cardId],
        ),
        returnValue: _i5.Future<_i6.CardModel?>.value(),
        returnValueForMissingStub: _i5.Future<_i6.CardModel?>.value(),
      ) as _i5.Future<_i6.CardModel?>);

  @override
  _i5.Future<List<_i6.CardModel>> searchCards(
    String? keyword, {
    int? bookId,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchCards,
          [keyword],
          {
            #bookId: bookId,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i5.Future<List<_i6.CardModel>>.value(<_i6.CardModel>[]),
        returnValueForMissingStub:
            _i5.Future<List<_i6.CardModel>>.value(<_i6.CardModel>[]),
      ) as _i5.Future<List<_i6.CardModel>>);

  @override
  _i5.Future<List<_i6.CardModel>> getNewCards({int? limit}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNewCards,
          [],
          {#limit: limit},
        ),
        returnValue: _i5.Future<List<_i6.CardModel>>.value(<_i6.CardModel>[]),
        returnValueForMissingStub:
            _i5.Future<List<_i6.CardModel>>.value(<_i6.CardModel>[]),
      ) as _i5.Future<List<_i6.CardModel>>);

  @override
  _i5.Future<List<_i6.CardModel>> getCardsForReview({int? limit}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCardsForReview,
          [],
          {#limit: limit},
        ),
        returnValue: _i5.Future<List<_i6.CardModel>>.value(<_i6.CardModel>[]),
        returnValueForMissingStub:
            _i5.Future<List<_i6.CardModel>>.value(<_i6.CardModel>[]),
      ) as _i5.Future<List<_i6.CardModel>>);

  @override
  _i5.Future<_i6.CardModel?> createCard({
    required int? bookId,
    required String? title,
    required String? question,
    required String? answer,
    String? type,
    int? typeVersion,
    dynamic extra,
    List<_i6.CardAsset>? assets,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCard,
          [],
          {
            #bookId: bookId,
            #title: title,
            #question: question,
            #answer: answer,
            #type: type,
            #typeVersion: typeVersion,
            #extra: extra,
            #assets: assets,
          },
        ),
        returnValue: _i5.Future<_i6.CardModel?>.value(),
        returnValueForMissingStub: _i5.Future<_i6.CardModel?>.value(),
      ) as _i5.Future<_i6.CardModel?>);

  @override
  _i5.Future<bool> updateCard(
    int? cardId, {
    String? title,
    String? question,
    String? answer,
    dynamic extra,
    List<_i6.CardAsset>? assets,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCard,
          [cardId],
          {
            #title: title,
            #question: question,
            #answer: answer,
            #extra: extra,
            #assets: assets,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> deleteCard(int? cardId) => (super.noSuchMethod(
        Invocation.method(
          #deleteCard,
          [cardId],
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> batchDeleteCards(List<int>? cardIds) => (super.noSuchMethod(
        Invocation.method(
          #batchDeleteCards,
          [cardIds],
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> moveCardsToBook(
    List<int>? cardIds,
    int? targetBookId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #moveCardsToBook,
          [
            cardIds,
            targetBookId,
          ],
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<List<_i6.CardAsset>> getCardAssets(int? cardId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCardAssets,
          [cardId],
        ),
        returnValue: _i5.Future<List<_i6.CardAsset>>.value(<_i6.CardAsset>[]),
        returnValueForMissingStub:
            _i5.Future<List<_i6.CardAsset>>.value(<_i6.CardAsset>[]),
      ) as _i5.Future<List<_i6.CardAsset>>);

  @override
  _i5.Future<Map<String, dynamic>> getCardStats(int? cardId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCardStats,
          [cardId],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
        returnValueForMissingStub:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<bool> syncCardsFromApi({int? bookId}) => (super.noSuchMethod(
        Invocation.method(
          #syncCardsFromApi,
          [],
          {#bookId: bookId},
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> manualSync({int? bookId}) => (super.noSuchMethod(
        Invocation.method(
          #manualSync,
          [],
          {#bookId: bookId},
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  void onInit() => super.noSuchMethod(
        Invocation.method(
          #onInit,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
        Invocation.method(
          #onReady,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
        Invocation.method(
          #onClose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
        Invocation.method(
          #$configureLifeCycle,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [BookDataService].
///
/// See the documentation for Mockito's code generation for more information.
class MockBookDataService extends _i1.Mock implements _i3.BookDataService {
  @override
  _i2.InternalFinalCallback<void> get onStart => (super.noSuchMethod(
        Invocation.getter(#onStart),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete => (super.noSuchMethod(
        Invocation.getter(#onDelete),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized => (super.noSuchMethod(
        Invocation.getter(#initialized),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i5.Future<_i3.BookDataService> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue:
            _i5.Future<_i3.BookDataService>.value(_FakeBookDataService_2(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i5.Future<_i3.BookDataService>.value(_FakeBookDataService_2(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
      ) as _i5.Future<_i3.BookDataService>);

  @override
  _i5.Future<List<_i6.BookModel>> getUserBooks({
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserBooks,
          [],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i5.Future<List<_i6.BookModel>>.value(<_i6.BookModel>[]),
        returnValueForMissingStub:
            _i5.Future<List<_i6.BookModel>>.value(<_i6.BookModel>[]),
      ) as _i5.Future<List<_i6.BookModel>>);

  @override
  _i5.Future<_i6.BookModel?> getBookById(int? bookId) => (super.noSuchMethod(
        Invocation.method(
          #getBookById,
          [bookId],
        ),
        returnValue: _i5.Future<_i6.BookModel?>.value(),
        returnValueForMissingStub: _i5.Future<_i6.BookModel?>.value(),
      ) as _i5.Future<_i6.BookModel?>);

  @override
  _i5.Future<List<_i6.BookModel>> searchBooks(
    String? keyword, {
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchBooks,
          [keyword],
          {
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i5.Future<List<_i6.BookModel>>.value(<_i6.BookModel>[]),
        returnValueForMissingStub:
            _i5.Future<List<_i6.BookModel>>.value(<_i6.BookModel>[]),
      ) as _i5.Future<List<_i6.BookModel>>);

  @override
  _i5.Future<_i6.BookModel?> createBook({
    required String? name,
    String? brief,
    String? cover,
    String? privacy,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createBook,
          [],
          {
            #name: name,
            #brief: brief,
            #cover: cover,
            #privacy: privacy,
          },
        ),
        returnValue: _i5.Future<_i6.BookModel?>.value(),
        returnValueForMissingStub: _i5.Future<_i6.BookModel?>.value(),
      ) as _i5.Future<_i6.BookModel?>);

  @override
  _i5.Future<bool> updateBook(
    int? bookId, {
    String? name,
    String? brief,
    String? cover,
    String? privacy,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateBook,
          [bookId],
          {
            #name: name,
            #brief: brief,
            #cover: cover,
            #privacy: privacy,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> deleteBook(int? bookId) => (super.noSuchMethod(
        Invocation.method(
          #deleteBook,
          [bookId],
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<Map<String, dynamic>> getBookStats(int? bookId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBookStats,
          [bookId],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
        returnValueForMissingStub:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> getUserBookStats() => (super.noSuchMethod(
        Invocation.method(
          #getUserBookStats,
          [],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
        returnValueForMissingStub:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<bool> syncBooksFromApi() => (super.noSuchMethod(
        Invocation.method(
          #syncBooksFromApi,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> manualSync() => (super.noSuchMethod(
        Invocation.method(
          #manualSync,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> syncLocalBooksToApi() => (super.noSuchMethod(
        Invocation.method(
          #syncLocalBooksToApi,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  void onInit() => super.noSuchMethod(
        Invocation.method(
          #onInit,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
        Invocation.method(
          #onReady,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
        Invocation.method(
          #onClose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
        Invocation.method(
          #$configureLifeCycle,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [UserDataService].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserDataService extends _i1.Mock implements _i3.UserDataService {
  @override
  _i2.InternalFinalCallback<void> get onStart => (super.noSuchMethod(
        Invocation.getter(#onStart),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete => (super.noSuchMethod(
        Invocation.getter(#onDelete),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized => (super.noSuchMethod(
        Invocation.getter(#initialized),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i5.Future<_i3.UserDataService> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue:
            _i5.Future<_i3.UserDataService>.value(_FakeUserDataService_3(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i5.Future<_i3.UserDataService>.value(_FakeUserDataService_3(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
      ) as _i5.Future<_i3.UserDataService>);

  @override
  _i5.Future<void> reloadCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #reloadCurrentUser,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool> syncCurrentUserFromApi() => (super.noSuchMethod(
        Invocation.method(
          #syncCurrentUserFromApi,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> loginUser(
    String? mobile,
    String? verificationCode,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #loginUser,
          [
            mobile,
            verificationCode,
          ],
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<void> logoutUser() => (super.noSuchMethod(
        Invocation.method(
          #logoutUser,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool> updateUserProfile({
    String? username,
    String? intro,
    String? avatar,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUserProfile,
          [],
          {
            #username: username,
            #intro: intro,
            #avatar: avatar,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> updateUserConfig({
    bool? isAutoPlayAudio,
    bool? isAutoPlayAiAudio,
    int? reviewNumber,
    int? studyNumber,
    int? studyType,
    int? currentStudyId,
    int? editingBookId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUserConfig,
          [],
          {
            #isAutoPlayAudio: isAutoPlayAudio,
            #isAutoPlayAiAudio: isAutoPlayAiAudio,
            #reviewNumber: reviewNumber,
            #studyNumber: studyNumber,
            #studyType: studyType,
            #currentStudyId: currentStudyId,
            #editingBookId: editingBookId,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
        returnValueForMissingStub: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<Map<String, dynamic>> getUserStats() => (super.noSuchMethod(
        Invocation.method(
          #getUserStats,
          [],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
        returnValueForMissingStub:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  bool isLoggedIn() => (super.noSuchMethod(
        Invocation.method(
          #isLoggedIn,
          [],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  void onInit() => super.noSuchMethod(
        Invocation.method(
          #onInit,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
        Invocation.method(
          #onReady,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
        Invocation.method(
          #onClose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
        Invocation.method(
          #$configureLifeCycle,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [DatabaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDatabaseService extends _i1.Mock implements _i3.DatabaseService {
  @override
  _i4.Database get database => (super.noSuchMethod(
        Invocation.getter(#database),
        returnValue: _FakeDatabase_4(
          this,
          Invocation.getter(#database),
        ),
        returnValueForMissingStub: _FakeDatabase_4(
          this,
          Invocation.getter(#database),
        ),
      ) as _i4.Database);

  @override
  _i2.InternalFinalCallback<void> get onStart => (super.noSuchMethod(
        Invocation.getter(#onStart),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete => (super.noSuchMethod(
        Invocation.getter(#onDelete),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized => (super.noSuchMethod(
        Invocation.getter(#initialized),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i5.Future<_i3.DatabaseService> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue:
            _i5.Future<_i3.DatabaseService>.value(_FakeDatabaseService_5(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i5.Future<_i3.DatabaseService>.value(_FakeDatabaseService_5(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
      ) as _i5.Future<_i3.DatabaseService>);

  @override
  _i5.Future<List<Map<String, dynamic>>> query(
    String? table, {
    bool? distinct,
    List<String>? columns,
    String? where,
    List<Object?>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #query,
          [table],
          {
            #distinct: distinct,
            #columns: columns,
            #where: where,
            #whereArgs: whereArgs,
            #groupBy: groupBy,
            #having: having,
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i5.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
        returnValueForMissingStub: _i5.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i5.Future<List<Map<String, dynamic>>>);

  @override
  _i5.Future<int> insert(
    String? table,
    Map<String, dynamic>? values,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #insert,
          [
            table,
            values,
          ],
        ),
        returnValue: _i5.Future<int>.value(0),
        returnValueForMissingStub: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<int> update(
    String? table,
    Map<String, dynamic>? values, {
    String? where,
    List<Object?>? whereArgs,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [
            table,
            values,
          ],
          {
            #where: where,
            #whereArgs: whereArgs,
          },
        ),
        returnValue: _i5.Future<int>.value(0),
        returnValueForMissingStub: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<int> delete(
    String? table, {
    String? where,
    List<Object?>? whereArgs,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [table],
          {
            #where: where,
            #whereArgs: whereArgs,
          },
        ),
        returnValue: _i5.Future<int>.value(0),
        returnValueForMissingStub: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<List<Map<String, dynamic>>> rawQuery(
    String? sql, [
    List<Object?>? arguments,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #rawQuery,
          [
            sql,
            arguments,
          ],
        ),
        returnValue: _i5.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
        returnValueForMissingStub: _i5.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i5.Future<List<Map<String, dynamic>>>);

  @override
  _i5.Future<void> rawExecute(
    String? sql, [
    List<Object?>? arguments,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #rawExecute,
          [
            sql,
            arguments,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<T> transaction<T>(
          _i5.Future<T> Function(_i4.Transaction)? action) =>
      (super.noSuchMethod(
        Invocation.method(
          #transaction,
          [action],
        ),
        returnValue: _i7.ifNotNull(
              _i7.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #transaction,
                  [action],
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_6<T>(
              this,
              Invocation.method(
                #transaction,
                [action],
              ),
            ),
        returnValueForMissingStub: _i7.ifNotNull(
              _i7.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #transaction,
                  [action],
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_6<T>(
              this,
              Invocation.method(
                #transaction,
                [action],
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i5.Future<List<Object?>> batch(void Function(_i4.Batch)? operations) =>
      (super.noSuchMethod(
        Invocation.method(
          #batch,
          [operations],
        ),
        returnValue: _i5.Future<List<Object?>>.value(<Object?>[]),
        returnValueForMissingStub: _i5.Future<List<Object?>>.value(<Object?>[]),
      ) as _i5.Future<List<Object?>>);

  @override
  _i5.Future<void> clearAllData() => (super.noSuchMethod(
        Invocation.method(
          #clearAllData,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  void onInit() => super.noSuchMethod(
        Invocation.method(
          #onInit,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
        Invocation.method(
          #onReady,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
        Invocation.method(
          #onClose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
        Invocation.method(
          #$configureLifeCycle,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
