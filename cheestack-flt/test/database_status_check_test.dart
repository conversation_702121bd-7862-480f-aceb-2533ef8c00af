import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/models/index.dart';

/// 数据库状态检查测试
/// 用于检查本地数据库的实际状态，帮助诊断卡片列表为空的问题
void main() {
  group('数据库状态检查', () {

    setUp(() {
      Get.reset();
    });

    tearDown(() {
      Get.reset();
    });

    test('检查服务注册状态', () async {
      print('');
      print('🔍 检查服务注册状态');
      print('=' * 40);

      // 检查各种服务是否已注册
      final services = {
        'DatabaseService': Get.isRegistered<DatabaseService>(),
        'StorageService': Get.isRegistered<StorageService>(),
        'CardDataService': Get.isRegistered<CardDataService>(),
        'BookDataService': Get.isRegistered<BookDataService>(),
        'UserDataService': Get.isRegistered<UserDataService>(),
      };

      print('📋 服务注册状态:');
      for (final entry in services.entries) {
        final status = entry.value ? '✅ 已注册' : '❌ 未注册';
        print('  - ${entry.key}: $status');
      }

      // 如果关键服务未注册，这可能是问题的原因
      if (!services['CardDataService']!) {
        print('⚠️  CardDataService未注册，这可能导致卡片操作失败');
      }

      if (!services['UserDataService']!) {
        print('⚠️  UserDataService未注册，这可能导致用户认证问题');
      }
    });

    test('检查应用初始化状态', () async {
      print('');
      print('🔍 检查应用初始化状态');
      print('=' * 40);

      print('📋 需要检查的初始化步骤:');
      print('1. 数据库服务初始化');
      print('2. 存储服务初始化');
      print('3. 数据访问服务初始化');
      print('4. 用户认证状态');
      print('5. 数据同步状态');
      print('');

      print('💡 如果应用未正确初始化，可能导致:');
      print('- 服务未注册');
      print('- 数据库连接失败');
      print('- 用户认证失败');
      print('- 数据查询返回空结果');
    });

    test('检查用户认证相关问题', () async {
      print('');
      print('🔍 检查用户认证相关问题');
      print('=' * 40);

      print('📋 可能的认证问题:');
      print('1. 用户未登录');
      print('2. 登录信息过期');
      print('3. 用户ID为空或无效');
      print('4. Token失效');
      print('5. 本地用户数据缺失');
      print('');

      print('🔍 检查方法:');
      print('- StorageService.to.getBool("isLogin")');
      print('- StorageService.to.getString("token")');
      print('- StorageService.to.getString("current_user_id")');
      print('- UserDataService.to.currentUser');
      print('');

      print('💡 如果用户认证有问题，会导致:');
      print('- 数据查询返回空结果');
      print('- 权限验证失败');
      print('- 数据保存失败');
    });

    test('检查数据查询相关问题', () async {
      print('');
      print('🔍 检查数据查询相关问题');
      print('=' * 40);

      print('📋 可能的数据查询问题:');
      print('1. 查询条件错误');
      print('2. 用户ID不匹配');
      print('3. 书籍ID缺失');
      print('4. 数据库表为空');
      print('5. 权限验证失败');
      print('');

      print('🔍 关键查询方法:');
      print('- CardDataService.getUserCards()');
      print('- CardDataService.getBookCards()');
      print('- BookDataService.getUserBooks()');
      print('');

      print('💡 如果数据查询有问题，会导致:');
      print('- 卡片列表显示为空');
      print('- 书籍列表显示为空');
      print('- 搜索功能失效');
      print('- 数据统计错误');
    });

    test('生成诊断总结', () {
      print('');
      print('🎯 诊断总结和建议');
      print('=' * 50);
      print('');
      print('基于以上检查结果，可能的问题和解决方案:');
      print('');
      print('1. 如果数据库表不存在:');
      print('   - 重新初始化数据库');
      print('   - 检查数据库迁移脚本');
      print('');
      print('2. 如果用户未登录:');
      print('   - 引导用户重新登录');
      print('   - 检查认证流程');
      print('');
      print('3. 如果卡片数据为空:');
      print('   - 检查卡片创建流程');
      print('   - 验证数据保存逻辑');
      print('   - 尝试从服务器同步数据');
      print('');
      print('4. 如果服务未注册:');
      print('   - 检查应用初始化流程');
      print('   - 确保服务正确注册');
      print('');
      print('5. 如果数据查询失败:');
      print('   - 检查查询条件');
      print('   - 验证权限设置');
      print('   - 查看详细错误日志');
      print('');
      print('=' * 50);
    });
  });
}
