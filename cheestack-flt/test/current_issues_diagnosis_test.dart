import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/shared/utils/error_handler.dart';

/// 当前问题诊断测试
/// 用于诊断用户报告的三个问题：
/// 1. 卡片编辑时出现"数据格式错误"toast
/// 2. 卡片保存时弹出错误toast
/// 3. 卡片列表界面没有显示任何卡片
void main() {
  group('当前问题诊断测试', () {
    group('问题1: 卡片编辑时数据格式错误', () {
      test('诊断可能导致"数据格式错误"的场景', () {
        // 测试ErrorHandler对各种格式错误的处理
        final formatErrors = [
          'JSON parse error',
          'Invalid format',
          'Parse failed',
          'Format exception',
          'Unexpected character in JSON',
          'Invalid JSON format',
          'Data format error',
          '解析错误',
          '格式错误',
          '数据格式错误'
        ];

        for (final error in formatErrors) {
          final message = ErrorHandler.getUserFriendlyMessage(error);
          print('错误: "$error" -> 用户消息: "$message"');
          
          // 验证是否会产生"数据格式错误"的消息
          if (message.contains('数据格式错误')) {
            print('✅ 找到可能的原因: $error');
          }
        }

        // 验证FormatException的处理
        final formatException = FormatException('Invalid JSON');
        final exceptionMessage = ErrorHandler.getUserFriendlyMessage(formatException);
        expect(exceptionMessage, contains('数据格式错误'));
        print('✅ FormatException处理正确: $exceptionMessage');
      });

      test('诊断JSON解析相关的错误', () {
        // 模拟各种可能导致JSON解析错误的场景
        final jsonErrors = [
          'Unexpected character',
          'Invalid JSON',
          'JSON decode failed',
          'Malformed JSON',
          'JSON syntax error'
        ];

        for (final error in jsonErrors) {
          final message = ErrorHandler.getUserFriendlyMessage(error);
          print('JSON错误: "$error" -> 用户消息: "$message"');
        }
      });
    });

    group('问题2: 卡片保存时toast错误', () {
      test('诊断可能的保存错误场景', () {
        // 测试各种可能导致保存失败的错误
        final saveErrors = [
          'Database connection failed',
          'Network error',
          'Connection refused',
          'Timeout',
          'Server error',
          'Validation failed',
          'Permission denied',
          'Disk full',
          'Database locked',
          'Constraint violation'
        ];

        for (final error in saveErrors) {
          final message = ErrorHandler.getUserFriendlyMessage(error);
          print('保存错误: "$error" -> 用户消息: "$message"');
        }
      });

      test('诊断网络相关的错误', () {
        final networkErrors = [
          'Connection refused',
          'Network unreachable',
          'Timeout',
          'No internet connection',
        ];

        for (final error in networkErrors) {
          final message = ErrorHandler.getUserFriendlyMessage(error);
          print('网络错误: "$error" -> 用户消息: "$message"');
          // 验证网络错误得到合适的处理
          expect(message, isNotEmpty);
        }

        // 单独测试DNS错误
        final dnsError = 'DNS resolution failed';
        final dnsMessage = ErrorHandler.getUserFriendlyMessage(dnsError);
        print('DNS错误: "$dnsError" -> 用户消息: "$dnsMessage"');
      });

      test('诊断数据验证错误', () {
        final validationErrors = [
          'Validation failed: title cannot be empty',
          'Required field missing',
          'Invalid data format',
          'Field too long',
          'Invalid characters'
        ];

        for (final error in validationErrors) {
          final message = ErrorHandler.getUserFriendlyMessage(error);
          print('验证错误: "$error" -> 用户消息: "$message"');
        }
      });
    });

    group('问题3: 卡片列表为空', () {
      test('诊断可能导致列表为空的原因', () {
        print('🔍 诊断卡片列表为空的可能原因:');
        print('');
        print('1. 本地数据库问题:');
        print('   - 数据库未初始化');
        print('   - 数据库连接失败');
        print('   - 表结构不正确');
        print('   - 数据被意外清除');
        print('');
        print('2. 用户认证问题:');
        print('   - 用户未登录');
        print('   - 用户ID为空');
        print('   - 认证信息过期');
        print('');
        print('3. 数据查询问题:');
        print('   - 查询条件错误');
        print('   - 书籍ID缺失');
        print('   - 权限不足');
        print('');
        print('4. 数据同步问题:');
        print('   - 本地数据未同步');
        print('   - 同步失败');
        print('   - 网络问题导致数据丢失');
        print('');
        print('5. 代码逻辑问题:');
        print('   - 服务未注册');
        print('   - 方法调用错误');
        print('   - 异常处理不当');
      });

      test('检查错误处理的完整性', () {
        // 测试各种可能的错误情况
        final testCases = [
          null,
          '',
          'Unknown error',
          Exception('Test exception'),
          FormatException('Format error'),
          {'msg': 'Error message'},
          {'error': 'Something went wrong'},
        ];

        for (final testCase in testCases) {
          final message = ErrorHandler.getUserFriendlyMessage(testCase);
          expect(message, isNotEmpty, reason: '错误消息不应为空');
          print('测试用例: $testCase -> 消息: "$message"');
        }
      });
    });

    group('数据库状态检查', () {
      test('检查数据库初始化状态', () {
        print('');
        print('🔍 数据库状态检查');
        print('=' * 30);
        print('');
        print('需要检查的项目:');
        print('1. 数据库文件是否存在');
        print('2. 数据库表结构是否正确');
        print('3. 用户表是否有数据');
        print('4. 卡片表是否有数据');
        print('5. 书籍表是否有数据');
        print('');
        print('检查方法:');
        print('- 使用DatabaseService.to.database访问数据库');
        print('- 执行SELECT COUNT(*) FROM cards查询卡片数量');
        print('- 执行SELECT COUNT(*) FROM books查询书籍数量');
        print('- 执行SELECT COUNT(*) FROM users查询用户数量');
        print('');
      });

      test('检查用户登录状态', () {
        print('');
        print('🔍 用户登录状态检查');
        print('=' * 30);
        print('');
        print('需要检查的项目:');
        print('1. StorageService中的isLogin标记');
        print('2. StorageService中的token');
        print('3. StorageService中的current_user_id');
        print('4. UserDataService.currentUser是否为null');
        print('');
        print('检查方法:');
        print('- StorageService.to.getBool("isLogin")');
        print('- StorageService.to.getString("token")');
        print('- StorageService.to.getString("current_user_id")');
        print('- UserDataService.to.currentUser');
        print('');
      });

      test('检查服务注册状态', () {
        print('');
        print('🔍 服务注册状态检查');
        print('=' * 30);
        print('');
        print('需要检查的服务:');
        print('1. CardDataService是否已注册');
        print('2. BookDataService是否已注册');
        print('3. UserDataService是否已注册');
        print('4. DatabaseService是否已注册');
        print('');
        print('检查方法:');
        print('- Get.isRegistered<CardDataService>()');
        print('- Get.isRegistered<BookDataService>()');
        print('- Get.isRegistered<UserDataService>()');
        print('- Get.isRegistered<DatabaseService>()');
        print('');
      });
    });

    group('综合诊断', () {
      test('生成问题诊断报告', () {
        print('');
        print('🔍 问题诊断报告');
        print('=' * 50);
        print('');
        print('问题1: 卡片编辑时数据格式错误');
        print('可能原因:');
        print('- JSON解析失败');
        print('- 数据格式不正确');
        print('- 网络响应格式错误');
        print('- 本地数据损坏');
        print('');
        print('问题2: 卡片保存时toast错误');
        print('可能原因:');
        print('- 网络连接问题');
        print('- 数据验证失败');
        print('- 数据库操作失败');
        print('- 权限不足');
        print('');
        print('问题3: 卡片列表为空');
        print('可能原因:');
        print('- 本地数据库为空');
        print('- 用户未登录');
        print('- 数据查询条件错误');
        print('- 数据同步失败');
        print('');
        print('建议的调试步骤:');
        print('1. 检查网络连接状态');
        print('2. 验证用户登录状态');
        print('3. 检查本地数据库内容');
        print('4. 查看详细错误日志');
        print('5. 测试数据同步功能');
        print('');
        print('=' * 50);
      });
    });
  });
}
